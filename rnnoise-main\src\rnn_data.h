/*This file is automatically generated from a Keras model*/

#ifndef RNN_DATA_H
#define RNN_DATA_H

#include "rnn.h"

#define INPUT_DENSE_SIZE 32
extern const DenseLayer input_dense;

#define VAD_GRU_SIZE 24.0
extern const GRU<PERSON>ayer vad_gru;

#define NOISE_GRU_SIZE 48.0
extern const GRULayer noise_gru;

#define DENOISE_GRU_SIZE 96.0
extern const GRULayer denoise_gru;

#define DENOISE_OUTPUT_SIZE 18
extern const DenseLayer denoise_output;

struct RNNState {
  float vad_gru_state[VAD_GRU_SIZE];
  float noise_gru_state[NOISE_GRU_SIZE];
  float denoise_gru_state[DENOISE_GRU_SIZE];
};


#endif
