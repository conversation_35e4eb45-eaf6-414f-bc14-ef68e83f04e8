#!/usr/bin/env python3
"""
检查训练数据的范围和统计信息
"""

import h5py
import numpy as np

print('Loading enhanced training data...')
with h5py.File('training_data_enhanced.h5', 'r') as hf:
    all_data = hf['data'][:]
print('done.')

print(f'Data shape: {all_data.shape}')
print(f'Data type: {all_data.dtype}')

# 检查各个部分的统计信息
input_features = all_data[:, :68]
noise_targets = all_data[:, 68:86]
voice_targets = all_data[:, 86:104]
vad_targets = all_data[:, 104:105]

print('\nInput features (68 dims):')
print(f'  Min: {np.min(input_features):.6f}')
print(f'  Max: {np.max(input_features):.6f}')
print(f'  Mean: {np.mean(input_features):.6f}')
print(f'  Std: {np.std(input_features):.6f}')
print(f'  NaN count: {np.sum(np.isnan(input_features))}')
print(f'  Inf count: {np.sum(np.isinf(input_features))}')

print('\nNoise suppression targets (18 dims):')
print(f'  Min: {np.min(noise_targets):.6f}')
print(f'  Max: {np.max(noise_targets):.6f}')
print(f'  Mean: {np.mean(noise_targets):.6f}')
print(f'  Std: {np.std(noise_targets):.6f}')
print(f'  NaN count: {np.sum(np.isnan(noise_targets))}')
print(f'  Inf count: {np.sum(np.isinf(noise_targets))}')

print('\nVoice enhancement targets (18 dims):')
print(f'  Min: {np.min(voice_targets):.6f}')
print(f'  Max: {np.max(voice_targets):.6f}')
print(f'  Mean: {np.mean(voice_targets):.6f}')
print(f'  Std: {np.std(voice_targets):.6f}')
print(f'  NaN count: {np.sum(np.isnan(voice_targets))}')
print(f'  Inf count: {np.sum(np.isinf(voice_targets))}')

print('\nVAD targets (1 dim):')
print(f'  Min: {np.min(vad_targets):.6f}')
print(f'  Max: {np.max(vad_targets):.6f}')
print(f'  Mean: {np.mean(vad_targets):.6f}')
print(f'  Std: {np.std(vad_targets):.6f}')
print(f'  NaN count: {np.sum(np.isnan(vad_targets))}')
print(f'  Inf count: {np.sum(np.isinf(vad_targets))}')

# 检查是否有异常值
print('\nChecking for extreme values...')
if np.any(input_features > 1000) or np.any(input_features < -1000):
    print('WARNING: Input features have extreme values!')
    
if np.any(noise_targets > 1) or np.any(noise_targets < 0):
    print('WARNING: Noise targets outside [0,1] range!')
    
if np.any(voice_targets > 1) or np.any(voice_targets < 0):
    print('WARNING: Voice targets outside [0,1] range!')
    
if np.any(vad_targets > 1) or np.any(vad_targets < 0):
    print('WARNING: VAD targets outside [0,1] range!')

print('\nData check completed.')
