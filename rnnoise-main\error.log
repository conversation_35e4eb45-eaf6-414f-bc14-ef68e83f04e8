2025-08-04 20:53:28.303655: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-04 20:53:30.111918: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\ops\operation.py", line 234, in from_config
    return cls(**config)
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\layers\core\dense.py", line 95, in __init__
    self.kernel_constraint = constraints.get(kernel_constraint)
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\constraints\__init__.py", line 47, in get
    obj = deserialize(identifier)
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\constraints\__init__.py", line 34, in deserialize
    return serialization_lib.deserialize_keras_object(
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\serialization_lib.py", line 706, in deserialize_keras_object
    raise TypeError(
TypeError: Unable to reconstruct an instance of 'WeightClip' because the class is missing a `from_config()` method. Full object config: {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\serialization_lib.py", line 718, in deserialize_keras_object
    instance = cls.from_config(inner_config)
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\ops\operation.py", line 236, in from_config
    raise TypeError(
TypeError: Error when deserializing class 'Dense' using config={'name': 'input_dense', 'trainable': True, 'dtype': 'float32', 'units': 24, 'activation': 'tanh', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}.

Exception encountered: Unable to reconstruct an instance of 'WeightClip' because the class is missing a `from_config()` method. Full object config: {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\serialization_lib.py", line 718, in deserialize_keras_object
    instance = cls.from_config(inner_config)
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\models\model.py", line 526, in from_config
    return functional_from_config(
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\models\functional.py", line 499, in functional_from_config
    process_layer(layer_data)
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\models\functional.py", line 483, in process_layer
    layer = serialization_lib.deserialize_keras_object(
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\serialization_lib.py", line 720, in deserialize_keras_object
    raise TypeError(
TypeError: <class 'keras.src.layers.core.dense.Dense'> could not be deserialized properly. Please ensure that components that are Python object instances (layers, models, etc.) returned by `get_config()` are explicitly deserialized in the model's `from_config()` method.

config={'module': 'keras.layers', 'class_name': 'Dense', 'config': {'name': 'input_dense', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None}, 'units': 24, 'activation': 'tanh', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 38]}, 'name': 'input_dense', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 38], 'dtype': 'float32', 'keras_history': ['main_input', 0, 0]}}], 'kwargs': {}}]}.

Exception encountered: Error when deserializing class 'Dense' using config={'name': 'input_dense', 'trainable': True, 'dtype': 'float32', 'units': 24, 'activation': 'tanh', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}.

Exception encountered: Unable to reconstruct an instance of 'WeightClip' because the class is missing a `from_config()` method. Full object config: {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\RNN\rnnoise-main\training\dump_rnn.py", line 99, in <module>
    model = load_model(
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\saving_api.py", line 189, in load_model
    return saving_lib.load_model(
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\saving_lib.py", line 365, in load_model
    return _load_model_from_fileobj(
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\saving_lib.py", line 442, in _load_model_from_fileobj
    model = _model_from_config(
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\saving_lib.py", line 431, in _model_from_config
    model = deserialize_keras_object(
  File "C:\Users\<USER>\Miniconda3\lib\site-packages\keras\src\saving\serialization_lib.py", line 720, in deserialize_keras_object
    raise TypeError(
TypeError: <class 'keras.src.models.functional.Functional'> could not be deserialized properly. Please ensure that components that are Python object instances (layers, models, etc.) returned by `get_config()` are explicitly deserialized in the model's `from_config()` method.

config={'module': 'keras.src.models.functional', 'class_name': 'Functional', 'config': {'name': 'functional', 'trainable': True, 'layers': [{'module': 'keras.layers', 'class_name': 'InputLayer', 'config': {'batch_shape': [None, None, 38], 'dtype': 'float32', 'sparse': False, 'name': 'main_input'}, 'registered_name': None, 'name': 'main_input', 'inbound_nodes': []}, {'module': 'keras.layers', 'class_name': 'Dense', 'config': {'name': 'input_dense', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None}, 'units': 24, 'activation': 'tanh', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 38]}, 'name': 'input_dense', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 38], 'dtype': 'float32', 'keras_history': ['main_input', 0, 0]}}], 'kwargs': {}}]}, {'module': 'keras.layers', 'class_name': 'GRU', 'config': {'name': 'vad_gru', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None, 'shared_object_id': 1818148822208}, 'return_sequences': True, 'return_state': False, 'go_backwards': False, 'stateful': False, 'unroll': False, 'zero_output_for_mask': False, 'units': 24, 'activation': 'tanh', 'recurrent_activation': 'sigmoid', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None, 'shared_object_id': 1818148827248}, 'recurrent_initializer': {'module': 'keras.initializers', 'class_name': 'OrthogonalInitializer', 'config': {'seed': None, 'gain': 1.0}, 'registered_name': None, 'shared_object_id': 1818148827344}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None, 'shared_object_id': 1818148827440}, 'kernel_regularizer': {'module': 'keras.regularizers', 'class_name': 'L2', 'config': {'l2': 1e-06}, 'registered_name': None, 'shared_object_id': 1818148823792}, 'recurrent_regularizer': {'module': 'keras.regularizers', 'class_name': 'L2', 'config': {'l2': 1e-06}, 'registered_name': None, 'shared_object_id': 1818148824080}, 'bias_regularizer': None, 'activity_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'recurrent_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'dropout': 0.0, 'recurrent_dropout': 0.0, 'reset_after': True, 'seed': None}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 24]}, 'name': 'vad_gru', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 24], 'dtype': 'float32', 'keras_history': ['input_dense', 0, 0]}}], 'kwargs': {'training': False, 'mask': None}}]}, {'module': 'keras.layers', 'class_name': 'Concatenate', 'config': {'name': 'concatenate', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None, 'shared_object_id': 1818148822208}, 'axis': -1}, 'registered_name': None, 'build_config': {'input_shape': [[None, None, 24], [None, None, 24], [None, None, 38]]}, 'name': 'concatenate', 'inbound_nodes': [{'args': [[{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 24], 'dtype': 'float32', 'keras_history': ['input_dense', 0, 0]}}, {'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 24], 'dtype': 'float32', 'keras_history': ['vad_gru', 0, 0]}}, {'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 38], 'dtype': 'float32', 'keras_history': ['main_input', 0, 0]}}]], 'kwargs': {}}]}, {'module': 'keras.layers', 'class_name': 'GRU', 'config': {'name': 'noise_gru', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None, 'shared_object_id': 1818148822208}, 'return_sequences': True, 'return_state': False, 'go_backwards': False, 'stateful': False, 'unroll': False, 'zero_output_for_mask': False, 'units': 48, 'activation': 'relu', 'recurrent_activation': 'sigmoid', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None, 'shared_object_id': 1818166970784}, 'recurrent_initializer': {'module': 'keras.initializers', 'class_name': 'OrthogonalInitializer', 'config': {'seed': None, 'gain': 1.0}, 'registered_name': None, 'shared_object_id': 1818166970880}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None, 'shared_object_id': 1818166970976}, 'kernel_regularizer': {'module': 'keras.regularizers', 'class_name': 'L2', 'config': {'l2': 1e-06}, 'registered_name': None, 'shared_object_id': 1818148830944}, 'recurrent_regularizer': {'module': 'keras.regularizers', 'class_name': 'L2', 'config': {'l2': 1e-06}, 'registered_name': None, 'shared_object_id': 1818166969200}, 'bias_regularizer': None, 'activity_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'recurrent_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'dropout': 0.0, 'recurrent_dropout': 0.0, 'reset_after': True, 'seed': None}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 86]}, 'name': 'noise_gru', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 86], 'dtype': 'float32', 'keras_history': ['concatenate', 0, 0]}}], 'kwargs': {'training': False, 'mask': None}}]}, {'module': 'keras.layers', 'class_name': 'Concatenate', 'config': {'name': 'concatenate_1', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None, 'shared_object_id': 1818148822208}, 'axis': -1}, 'registered_name': None, 'build_config': {'input_shape': [[None, None, 24], [None, None, 48], [None, None, 38]]}, 'name': 'concatenate_1', 'inbound_nodes': [{'args': [[{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 24], 'dtype': 'float32', 'keras_history': ['vad_gru', 0, 0]}}, {'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 48], 'dtype': 'float32', 'keras_history': ['noise_gru', 0, 0]}}, {'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 38], 'dtype': 'float32', 'keras_history': ['main_input', 0, 0]}}]], 'kwargs': {}}]}, {'module': 'keras.layers', 'class_name': 'GRU', 'config': {'name': 'denoise_gru', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None, 'shared_object_id': 1818148822208}, 'return_sequences': True, 'return_state': False, 'go_backwards': False, 'stateful': False, 'unroll': False, 'zero_output_for_mask': False, 'units': 96, 'activation': 'tanh', 'recurrent_activation': 'sigmoid', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None, 'shared_object_id': 1818166975296}, 'recurrent_initializer': {'module': 'keras.initializers', 'class_name': 'OrthogonalInitializer', 'config': {'seed': None, 'gain': 1.0}, 'registered_name': None, 'shared_object_id': 1818166975392}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None, 'shared_object_id': 1818166975488}, 'kernel_regularizer': {'module': 'keras.regularizers', 'class_name': 'L2', 'config': {'l2': 1e-06}, 'registered_name': None, 'shared_object_id': 1818166973232}, 'recurrent_regularizer': {'module': 'keras.regularizers', 'class_name': 'L2', 'config': {'l2': 1e-06}, 'registered_name': None, 'shared_object_id': 1818166973712}, 'bias_regularizer': None, 'activity_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'recurrent_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'dropout': 0.0, 'recurrent_dropout': 0.0, 'reset_after': True, 'seed': None}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 110]}, 'name': 'denoise_gru', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 110], 'dtype': 'float32', 'keras_history': ['concatenate_1', 0, 0]}}], 'kwargs': {'training': False, 'mask': None}}]}, {'module': 'keras.layers', 'class_name': 'Dense', 'config': {'name': 'denoise_output', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None, 'shared_object_id': 1818148822208}, 'units': 18, 'activation': 'sigmoid', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 96]}, 'name': 'denoise_output', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 96], 'dtype': 'float32', 'keras_history': ['denoise_gru', 0, 0]}}], 'kwargs': {}}]}, {'module': 'keras.layers', 'class_name': 'Dense', 'config': {'name': 'voice_output', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None, 'shared_object_id': 1818148822208}, 'units': 18, 'activation': 'sigmoid', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 96]}, 'name': 'voice_output', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 96], 'dtype': 'float32', 'keras_history': ['denoise_gru', 0, 0]}}], 'kwargs': {}}]}, {'module': 'keras.layers', 'class_name': 'Dense', 'config': {'name': 'vad_output', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None, 'shared_object_id': 1818148822208}, 'units': 1, 'activation': 'sigmoid', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 24]}, 'name': 'vad_output', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 24], 'dtype': 'float32', 'keras_history': ['vad_gru', 0, 0]}}], 'kwargs': {}}]}], 'input_layers': [['main_input', 0, 0]], 'output_layers': [['denoise_output', 0, 0], ['voice_output', 0, 0], ['vad_output', 0, 0]]}, 'registered_name': 'Functional', 'build_config': {'input_shape': None}, 'compile_config': {'optimizer': {'module': 'keras.optimizers', 'class_name': 'Adam', 'config': {'name': 'adam', 'learning_rate': 0.0010000000474974513, 'weight_decay': None, 'clipnorm': None, 'global_clipnorm': None, 'clipvalue': None, 'use_ema': False, 'ema_momentum': 0.99, 'ema_overwrite_frequency': None, 'loss_scale_factor': None, 'gradient_accumulation_steps': None, 'beta_1': 0.9, 'beta_2': 0.999, 'epsilon': 1e-07, 'amsgrad': False}, 'registered_name': None}, 'loss': [{'module': 'builtins', 'class_name': 'function', 'config': 'mycost', 'registered_name': 'function', 'shared_object_id': 1818148979616}, {'module': 'builtins', 'class_name': 'function', 'config': 'mycost', 'registered_name': 'function', 'shared_object_id': 1818148979616}, {'module': 'builtins', 'class_name': 'function', 'config': 'my_crossentropy', 'registered_name': 'function'}], 'loss_weights': [10, 8, 0.5], 'metrics': [{'module': 'builtins', 'class_name': 'function', 'config': 'msse', 'registered_name': 'function', 'shared_object_id': 1818148979184}, {'module': 'builtins', 'class_name': 'function', 'config': 'msse', 'registered_name': 'function', 'shared_object_id': 1818148979184}, {'module': 'builtins', 'class_name': 'function', 'config': 'my_accuracy', 'registered_name': 'function'}], 'weighted_metrics': None, 'run_eagerly': False, 'steps_per_execution': 1, 'jit_compile': False}}.

Exception encountered: <class 'keras.src.layers.core.dense.Dense'> could not be deserialized properly. Please ensure that components that are Python object instances (layers, models, etc.) returned by `get_config()` are explicitly deserialized in the model's `from_config()` method.

config={'module': 'keras.layers', 'class_name': 'Dense', 'config': {'name': 'input_dense', 'trainable': True, 'dtype': {'module': 'keras', 'class_name': 'DTypePolicy', 'config': {'name': 'float32'}, 'registered_name': None}, 'units': 24, 'activation': 'tanh', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}, 'registered_name': None, 'build_config': {'input_shape': [None, None, 38]}, 'name': 'input_dense', 'inbound_nodes': [{'args': [{'class_name': '__keras_tensor__', 'config': {'shape': [None, None, 38], 'dtype': 'float32', 'keras_history': ['main_input', 0, 0]}}], 'kwargs': {}}]}.

Exception encountered: Error when deserializing class 'Dense' using config={'name': 'input_dense', 'trainable': True, 'dtype': 'float32', 'units': 24, 'activation': 'tanh', 'use_bias': True, 'kernel_initializer': {'module': 'keras.initializers', 'class_name': 'GlorotUniform', 'config': {'seed': None}, 'registered_name': None}, 'bias_initializer': {'module': 'keras.initializers', 'class_name': 'Zeros', 'config': {}, 'registered_name': None}, 'kernel_regularizer': None, 'bias_regularizer': None, 'kernel_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}, 'bias_constraint': {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}}.

Exception encountered: Unable to reconstruct an instance of 'WeightClip' because the class is missing a `from_config()` method. Full object config: {'module': None, 'class_name': 'WeightClip', 'config': {'name': 'WeightClip', 'c': 0.499}, 'registered_name': 'WeightClip', 'shared_object_id': 1817442709088}
