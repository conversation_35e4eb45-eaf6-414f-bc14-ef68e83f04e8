#!/usr/bin/python

from __future__ import print_function

import sys
import struct

def convert_f32_to_hdf5(input_file, rows, cols, output_file):
    """
    Convert binary f32 file to HDF5 format without external dependencies
    """
    try:
        # Try to import numpy and h5py first
        import numpy as np
        import h5py

        print(f"Converting {input_file} to {output_file}")
        print(f"Expected shape: ({rows}, {cols})")

        # Read binary data
        data = np.fromfile(input_file, dtype='float32')
        print(f"Read {len(data)} float32 values")

        # Check if data size matches expected dimensions
        expected_size = rows * cols
        if len(data) != expected_size:
            print(f"Warning: Data size {len(data)} doesn't match expected {expected_size}")
            # Adjust rows to fit actual data
            rows = len(data) // cols
            print(f"Adjusted rows to: {rows}")

        # Reshape data
        data = np.reshape(data, (rows, cols))
        print(f"Reshaped to: {data.shape}")

        # Create HDF5 file
        h5f = h5py.File(output_file, 'w')
        h5f.create_dataset('data', data=data)
        h5f.close()

        print(f"Successfully converted to {output_file}")

    except ImportError as e:
        print(f"Error: Missing required packages: {e}")
        print("Please install numpy and h5py")
        sys.exit(1)
    except Exception as e:
        print(f"Error during conversion: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 5:
        print("Usage: python bin2hdf5.py <input.f32> <rows> <cols> <output.h5>")
        sys.exit(1)

    input_file = sys.argv[1]
    rows = int(sys.argv[2])
    cols = int(sys.argv[3])
    output_file = sys.argv[4]

    convert_f32_to_hdf5(input_file, rows, cols, output_file)
