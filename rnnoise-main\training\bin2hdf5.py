#!/usr/bin/python

from __future__ import print_function

try:
    import numpy as np
    import h5py
except ImportError as e:
    print(f"Error: Required package not found: {e}")
    print("Please install required packages:")
    print("pip install -i https://pypi.tuna.tsinghua.edu.cn/simple numpy h5py")
    sys.exit(1)

import sys
import os

# 默认参数设置 - 直接处理您的数据
default_input = "../training_data_enhanced.f32"
default_output = "training_data_enhanced.h5"

def convert_data():
    """转换数据的主函数"""
    # 如果没有提供命令行参数，使用默认值
    if len(sys.argv) == 1:
        input_file = default_input
        output_file = default_output

        # 自动计算维度
        if os.path.exists(input_file):
            file_size = os.path.getsize(input_file)
            total_elements = file_size // 4  # 4 bytes per float32
            cols = 105  # 根据训练脚本的期望维度
            rows = total_elements // cols
            print(f"Auto-detected dimensions: {rows} x {cols}")
        else:
            print(f"Error: File {input_file} not found")
            return False
    else:
        # 使用命令行参数
        input_file = sys.argv[1]
        rows = int(sys.argv[2])
        cols = int(sys.argv[3])
        output_file = sys.argv[4]

    print(f"Converting {input_file} to {output_file}")
    print(f"Expected dimensions: {rows} x {cols}")

    try:
        # 读取数据
        print("Reading binary data...")
        data = np.fromfile(input_file, dtype='float32')
        print(f"Read {len(data):,} float32 values")

        # 调整数据大小以匹配完整的行
        actual_rows = len(data) // cols
        if actual_rows != rows:
            print(f"Adjusting rows from {rows} to {actual_rows} to match data size")
            rows = actual_rows

        # 确保数据大小正确
        data = data[:rows * cols]
        print(f"Using {len(data):,} values ({rows} x {cols})")

        # 重塑数据
        print("Reshaping data...")
        data = np.reshape(data, (rows, cols))
        print(f"Final shape: {data.shape}")

        # 保存为HDF5
        print("Saving to HDF5 format...")
        with h5py.File(output_file, 'w') as h5f:
            h5f.create_dataset('data', data=data, compression='gzip', compression_opts=9)

        print(f"✓ Conversion completed successfully!")
        print(f"✓ Output file: {output_file}")
        print(f"✓ Data shape: {data.shape}")
        return True

    except Exception as e:
        print(f"Error during conversion: {e}")
        return False

if __name__ == "__main__":
    success = convert_data()
    if not success:
        sys.exit(1)
