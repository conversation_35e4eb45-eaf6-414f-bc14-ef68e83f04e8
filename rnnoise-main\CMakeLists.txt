cmake_minimum_required(VERSION 3.5)
project(EnhancedRNNoise C)

# 设置编译选项
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -W -O3 -g -ffast-math")

# 包含目录
include_directories(include)

# 源文件
set(CORE_SOURCES
    src/kiss_fft.c
    src/celt_lpc.c
    src/pitch.c
    src/rnn.c
    src/denoise.c
)

# 原版RNN权重文件
set(ORIGINAL_SOURCES
    src/rnn_data.c
)

# 增强RNN权重文件
set(ENHANCED_SOURCES
    src/rnn_data_enhanced.c
)

# 可执行文件目标

# 1. 增强版训练数据生成程序
add_executable(denoise_training_enhanced
    ${CORE_SOURCES}
    ${ENHANCED_SOURCES}
)
target_compile_definitions(denoise_training_enhanced PRIVATE TRAINING=1 ENHANCED=1)
target_link_libraries(denoise_training_enhanced m)

# 2. 增强版推理程序
add_executable(rnnoise_enhanced
    ${CORE_SOURCES}
    ${ENHANCED_SOURCES}
    main.c
)
target_compile_definitions(rnnoise_enhanced PRIVATE ENHANCED=1)
target_link_libraries(rnnoise_enhanced m)

# 3. 原版训练数据生成程序（兼容性）
add_executable(denoise_training_original
    ${CORE_SOURCES}
    ${ORIGINAL_SOURCES}
)
target_compile_definitions(denoise_training_original PRIVATE TRAINING=1)
target_link_libraries(denoise_training_original m)

# 4. 原版推理程序（兼容性）
add_executable(rnnoise_original
    ${CORE_SOURCES}
    ${ORIGINAL_SOURCES}
    main.c
)
target_link_libraries(rnnoise_original m)

# 设置输出目录
set_target_properties(
    denoise_training_enhanced
    rnnoise_enhanced
    denoise_training_original
    rnnoise_original
    PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 创建models_enhance目录
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/models_enhance)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/models_enhance/checkpoints)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/models_enhance/best_models)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/models_enhance/final_weights)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/models_enhance/training_logs)

# 安装目标
install(TARGETS denoise_training_enhanced rnnoise_enhanced denoise_training_original rnnoise_original
    RUNTIME DESTINATION bin
)

# 打印信息
message(STATUS "Enhanced RNNoise build configuration:")
message(STATUS "  - Enhanced training data generator: denoise_training_enhanced")
message(STATUS "  - Enhanced inference program: rnnoise_enhanced")
message(STATUS "  - Original training data generator: denoise_training_original")
message(STATUS "  - Original inference program: rnnoise_original")
message(STATUS "  - Output directory: ${CMAKE_BINARY_DIR}/bin")
message(STATUS "  - Models directory: ${CMAKE_BINARY_DIR}/models_enhance")
