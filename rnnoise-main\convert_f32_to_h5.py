#!/usr/bin/env python3
"""
Simple script to convert f32 binary file to HDF5 format
This script tries to determine the correct dimensions automatically
"""

import sys
import os
import struct

def convert_f32_to_h5_simple(input_file, output_file):
    """
    Convert f32 binary file to HDF5 using a simple approach
    """
    try:
        import numpy as np
        import h5py
        
        print(f"Converting {input_file} to {output_file}")
        
        # Get file size
        file_size = os.path.getsize(input_file)
        print(f"File size: {file_size} bytes")
        
        # Calculate number of float32 values
        num_floats = file_size // 4
        print(f"Number of float32 values: {num_floats}")
        
        # Try different common dimensions
        possible_dims = [105, 87, 68, 42, 38]  # Common dimensions in RNNoise
        
        for dim in possible_dims:
            if num_floats % dim == 0:
                rows = num_floats // dim
                print(f"Found valid dimensions: {rows} x {dim}")
                
                # Read and reshape data
                data = np.fromfile(input_file, dtype='float32')
                data = np.reshape(data, (rows, dim))
                
                # Create HDF5 file
                with h5py.File(output_file, 'w') as h5f:
                    h5f.create_dataset('data', data=data)
                
                print(f"Successfully converted to {output_file}")
                print(f"Data shape: {data.shape}")
                return True
        
        # If no exact match, try with the most likely dimension (105)
        dim = 105
        rows = num_floats // dim
        remaining = num_floats % dim
        
        if remaining > 0:
            print(f"Warning: {remaining} extra values will be truncated")
            # Truncate to fit exact dimensions
            truncated_size = rows * dim
            
            # Read only the data that fits
            with open(input_file, 'rb') as f:
                data_bytes = f.read(truncated_size * 4)  # 4 bytes per float32
            
            data = np.frombuffer(data_bytes, dtype='float32')
            data = np.reshape(data, (rows, dim))
            
            # Create HDF5 file
            with h5py.File(output_file, 'w') as h5f:
                h5f.create_dataset('data', data=data)
            
            print(f"Successfully converted to {output_file}")
            print(f"Data shape: {data.shape}")
            print(f"Note: {remaining} values were truncated")
            return True
            
    except ImportError as e:
        print(f"Error: Missing required packages: {e}")
        print("Please install numpy and h5py")
        return False
    except Exception as e:
        print(f"Error during conversion: {e}")
        return False

def main():
    if len(sys.argv) != 3:
        print("Usage: python convert_f32_to_h5.py <input.f32> <output.h5>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"Error: Input file {input_file} does not exist")
        sys.exit(1)
    
    success = convert_f32_to_h5_simple(input_file, output_file)
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
