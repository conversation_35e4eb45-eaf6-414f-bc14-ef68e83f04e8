<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CMakeRunConfigurationManager" shouldGenerate="true" shouldDeleteObsolete="true" buildAllGenerated="true">
    <generated>
      <config projectName="rnn_gao_new" targetName="rnnLib" />
      <config projectName="rnn_gao_new" targetName="rnn_gao_new" />
    </generated>
  </component>
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="Debug" CONFIG_NAME="Debug" GENERATION_DIR="." />
    </configurations>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b42650e5-a3e3-4084-a653-cf15eda203a7" name="Default Changelist" comment="" />
    <ignored path="$PROJECT_DIR$/cmake-build-debug/" />
    <ignored path="$PROJECT_DIR$/cmake-build-release/" />
    <ignored path="$PROJECT_DIR$/CMakeFiles/" />
    <ignored path="$PROJECT_DIR$/src/CMakeFiles/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="CMakeBuildProfile:Debug" />
  <component name="FUSProjectUsageTrigger">
    <session id="-618933462">
      <usages-collector id="statistics.lifecycle.project">
        <counts>
          <entry key="project.closed" value="2" />
          <entry key="project.open.time.0" value="3" />
          <entry key="project.opened" value="3" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.edit">
        <counts>
          <entry key="CMakeLists.txt" value="38" />
          <entry key="ObjectiveC" value="311" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.open">
        <counts>
          <entry key="CMakeCache.txt" value="1" />
          <entry key="CMakeLists.txt" value="4" />
          <entry key="Disassembly" value="1" />
          <entry key="ObjectiveC" value="7" />
        </counts>
      </usages-collector>
    </session>
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/CMakeLists.txt">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="45">
              <caret line="3" column="24" selection-start-line="3" selection-start-column="24" selection-end-line="3" selection-end-column="24" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/main.c">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="191">
              <caret line="74" column="37" lean-forward="true" selection-start-line="74" selection-start-column="37" selection-end-line="74" selection-end-column="37" />
              <folding>
                <element signature="e#21#40#0" expanded="true" />
                <element signature="e#136#154#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/src/denoise.c">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="256">
              <caret line="582" column="25" lean-forward="true" selection-start-line="582" selection-start-column="25" selection-end-line="582" selection-end-column="25" />
              <folding>
                <element signature="e#1377#1396#0" expanded="true" />
                <element signature="e#1691#1710#0" expanded="true" />
                <element signature="e#26656#26849#0" expanded="true" />
                <element signature="e#27155#27543#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/denoise16.c">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="7245">
              <caret line="483" column="6" selection-start-line="483" selection-start-column="6" selection-end-line="483" selection-end-column="6" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="CMakeLists.txt" />
      </list>
    </option>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>wavRead_f32</find>
      <find>out</find>
      <find>rnnoise_process_frame</find>
      <find>in</find>
      <find>gf</find>
      <find>tmp2</find>
      <find>E_noise</find>
      <find>vad_prob</find>
      <find>noisy</find>
      <find>xn</find>
      <find>fout</find>
      <find>frames_count</find>
      <find>sprintf</find>
      <find>get_file_list</find>
      <find>f1</find>
      <find>open_file</find>
    </findStrings>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/src/CMakeLists.txt" />
        <option value="$PROJECT_DIR$/CMakeLists.txt" />
        <option value="$PROJECT_DIR$/main.c" />
        <option value="$PROJECT_DIR$/src/denoise.c" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="OCFindUsagesOptions" text="true" ivars="false" properties="true" derivedClasses="false" />
  <component name="ProjectFrameBounds" extendedState="4">
    <option name="x" value="67" />
    <option name="y" value="25" />
    <option name="width" value="927" />
    <option name="height" value="1055" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="rnn_gao_new" type="b2602c69:ProjectViewProjectNode" />
              <item name="rnn_gao_new" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="rnn_gao_new" type="b2602c69:ProjectViewProjectNode" />
              <item name="rnn_gao_new" type="462c0819:PsiDirectoryNode" />
              <item name="include" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="rnn_gao_new" type="b2602c69:ProjectViewProjectNode" />
              <item name="rnn_gao_new" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="settings.editor.selected.configurable" value="CMakeSettings" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Application.rnn_gao_new">
    <configuration name="Build All" type="CMakeRunConfiguration" factoryName="Application" PASS_PARENT_ENVS_2="true" CONFIG_NAME="Debug" EXPLICIT_BUILD_TARGET_NAME="all">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="rnnLib" type="CMakeRunConfiguration" factoryName="Application" PASS_PARENT_ENVS_2="true" PROJECT_NAME="rnn_gao_new" TARGET_NAME="rnnLib" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="rnn_gao_new" type="CMakeRunConfiguration" factoryName="Application" PROGRAM_PARAMS="$PROJECT_DIR$/../LearnC_train_wav/BAC009S0002W0122.wav $PROJECT_DIR$/../LearnC_train_wav/test.wav" PASS_PARENT_ENVS_2="true" PROJECT_NAME="rnn_gao_new" TARGET_NAME="rnn_gao_new" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="rnn_gao_new" RUN_TARGET_NAME="rnn_gao_new">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.Build All" />
      <item itemvalue="Application.rnn_gao_new" />
      <item itemvalue="Application.rnnLib" />
    </list>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b42650e5-a3e3-4084-a653-cf15eda203a7" name="Default Changelist" comment="" />
      <created>1557911392776</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1557911392776</updated>
      <workItem from="1557911393971" duration="14299000" />
      <workItem from="1558060727245" duration="4184000" />
      <workItem from="1558076151885" duration="5926000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="24409000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="67" y="25" width="927" height="1055" extended-state="4" />
    <editor active="true" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.22485876" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.32975295" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.39957035" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Database Changes" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Messages" order="8" visible="true" weight="0.32975295" />
      <window_info anchor="bottom" id="Terminal" order="9" weight="0.32975295" />
      <window_info anchor="bottom" id="Event Log" order="10" side_tool="true" />
      <window_info anchor="bottom" id="Version Control" order="11" show_stripe_button="false" />
      <window_info anchor="bottom" id="CMake" order="12" weight="0.32975295" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Database" order="3" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/cmake-build-debug/CMakeCache.txt" />
    <entry file="file://$PROJECT_DIR$/cmake-build-debug/cmake_install.cmake" />
    <entry file="file://$PROJECT_DIR$/src/CMakeLists.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="15">
          <caret line="1" column="22" selection-start-line="1" selection-start-column="22" selection-end-line="1" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file:///usr/include/stdio.h">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="301">
          <caret line="317" column="11" selection-start-line="317" selection-start-column="11" selection-end-line="317" selection-end-column="11" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/kiss_fft.h">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="591">
          <caret line="189" column="8" selection-start-line="189" selection-start-column="8" selection-end-line="189" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/include/dr_mp3.h">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="169">
          <caret line="2823" selection-start-line="2823" selection-end-line="2823" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/CMakeLists.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="45">
          <caret line="3" column="24" selection-start-line="3" selection-start-column="24" selection-end-line="3" selection-end-column="24" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/denoise16.c">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="7245">
          <caret line="483" column="6" selection-start-line="483" selection-start-column="6" selection-end-line="483" selection-end-column="6" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/main.c">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="191">
          <caret line="74" column="37" lean-forward="true" selection-start-line="74" selection-start-column="37" selection-end-line="74" selection-end-column="37" />
          <folding>
            <element signature="e#21#40#0" expanded="true" />
            <element signature="e#136#154#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/denoise.c">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="256">
          <caret line="582" column="25" lean-forward="true" selection-start-line="582" selection-start-column="25" selection-end-line="582" selection-end-column="25" />
          <folding>
            <element signature="e#1377#1396#0" expanded="true" />
            <element signature="e#1691#1710#0" expanded="true" />
            <element signature="e#26656#26849#0" expanded="true" />
            <element signature="e#27155#27543#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
</project>