#!/usr/bin/python

from __future__ import print_function
import tensorflow as tf
import keras
from keras.models import Sequential
from keras.models import Model
from keras.layers import Input
from keras.callbacks import ModelCheckpoint
from keras.layers import Dense
from keras.layers import LSTM
from keras.layers import GRU
from keras.layers import SimpleRNN
from keras.layers import Dropout
from keras.layers import concatenate
from keras import losses
from keras import regularizers
from keras.constraints import min_max_norm
import h5py
from keras.constraints import Constraint
import tensorflow as tf
from keras import backend as K
import numpy as np

# 设置CPU运行
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
#import tensorflow as tf
#from keras.backend.tensorflow_backend import set_session
#config = tf.ConfigProto()
#config.gpu_options.per_process_gpu_memory_fraction = 0.42
#set_session(tf.Session(config=config))


def my_crossentropy(y_true, y_pred):
    """适配VAD输出的交叉熵损失函数"""
    return tf.keras.losses.binary_crossentropy(y_true, y_pred)

def human_voice_enhancement_loss(y_true, y_pred):
    """专门的人声增强损失函数 - 只关注人声特有特征"""

    # 人声频段权重映射 (18个频段对应不同的人声特征重要性)
    # 基于人声基频和谐波分布：80-300Hz(基频), 300-3400Hz(主要谐波), 3400-8000Hz(高频谐波)
    voice_frequency_weights = tf.constant([
        # 低频段 (80-300Hz) - 基频区域，权重适中
        1.5, 1.8, 2.0,
        # 中频段 (300-1000Hz) - 人声核心频段，最高权重
        3.0, 3.5, 4.0, 3.8, 3.2,
        # 中高频段 (1000-3400Hz) - 重要谐波，高权重
        2.8, 2.5, 2.2, 2.0, 1.8,
        # 高频段 (3400-8000Hz) - 清晰度相关，中等权重
        1.5, 1.2, 1.0, 0.8, 0.6
    ], dtype=tf.float32)

    # 人声增强专用损失计算
    # 1. 谐波结构保持损失 - 确保人声谐波不被破坏
    harmonic_loss = tf.square(y_pred - y_true) * voice_frequency_weights

    # 2. 人声清晰度损失 - 使用对数域计算，更符合人耳感知
    clarity_loss = tf.square(tf.math.log(y_pred + 1e-8) - tf.math.log(y_true + 1e-8)) * voice_frequency_weights * 0.5

    # 3. 人声连续性损失 - 确保人声增强的时间连续性
    # 只在人声活跃区域计算（y_true > 0.1的区域）
    voice_mask = tf.cast(y_true > 0.1, tf.float32)
    continuity_loss = tf.square(y_pred - y_true) * voice_mask * 0.3

    # 4. 动态范围保持损失 - 保持人声的动态特征
    dynamic_loss = tf.abs(y_pred - y_true) * voice_frequency_weights * 0.2

    # 总的人声增强损失
    total_voice_loss = harmonic_loss + clarity_loss + continuity_loss + dynamic_loss

    return tf.reduce_mean(total_voice_loss, axis=-1)

def mymask(y_true):
    return tf.minimum(y_true+1., 1.)

def msse(y_true, y_pred):
    return tf.reduce_mean(mymask(y_true) * tf.square(tf.sqrt(y_pred) - tf.sqrt(y_true)), axis=-1)

def mycost(y_true, y_pred):
    # 确保输入维度正确
    mask = mymask(y_true)
    sqrt_diff = tf.sqrt(tf.maximum(y_pred, 1e-8)) - tf.sqrt(tf.maximum(y_true, 1e-8))
    mse_loss = tf.square(sqrt_diff)
    quartic_loss = tf.square(mse_loss)

    # 简化损失函数，避免维度问题
    total_loss = mask * (10 * quartic_loss + mse_loss)
    return tf.reduce_mean(total_loss, axis=-1)

def my_accuracy(y_true, y_pred):
    return tf.reduce_mean(2*tf.abs(y_true-0.5) * tf.cast(tf.equal(y_true, tf.round(y_pred)), tf.float32), axis=-1)

# 人声增强损失函数
def voice_enhancement_loss(y_true, y_pred):
    """人声增强损失函数"""
    mask = tf.minimum(y_true + 1., 1.)

    # 基础MSE损失，确保数值稳定性
    sqrt_pred = tf.sqrt(tf.maximum(y_pred, 1e-8))
    sqrt_true = tf.sqrt(tf.maximum(y_true, 1e-8))
    mse = tf.square(sqrt_pred - sqrt_true)

    # 高频增强权重 (对高频成分给予更多关注)
    freq_weights = tf.constant([1.0, 1.0, 1.1, 1.1, 1.2, 1.2, 1.3, 1.3, 1.4,
                               1.4, 1.5, 1.5, 1.6, 1.6, 1.7, 1.7, 1.8, 1.8])
    freq_weights = tf.reshape(freq_weights, [1, 1, 18])  # 确保维度匹配
    weighted_mse = mse * freq_weights

    # 谐波保持损失 (确保自然度)
    harmonic_loss = tf.square(y_pred - y_true) * 0.1

    total_loss = weighted_mse + harmonic_loss
    return tf.reduce_mean(mask * total_loss, axis=-1)

# 组合损失函数 (用于单输出模型的兼容性)
def combined_enhancement_loss(y_true, y_pred):
    """组合的噪声抑制和人声增强损失"""
    # y_true和y_pred都是36维 (18维噪声抑制 + 18维人声增强)
    noise_true = y_true[:, :, :18]
    noise_pred = y_pred[:, :, :18]
    voice_true = y_true[:, :, 18:]
    voice_pred = y_pred[:, :, 18:]

    noise_loss = mycost(noise_true, noise_pred)
    voice_loss = voice_enhancement_loss(voice_true, voice_pred)

    # 加权组合
    return 10.0 * noise_loss + 8.0 * voice_loss

class WeightClip(Constraint):
    '''Clips the weights incident to each hidden unit to be inside a range
    '''
    def __init__(self, c=2):
        self.c = c

    def __call__(self, p):
        return tf.clip_by_value(p, -self.c, self.c)

    def get_config(self):
        return {'name': self.__class__.__name__,
            'c': self.c}


reg = 0.000001
constraint = WeightClip(0.499)

print('Build enhanced model with specialized voice processing...')
print('Key improvements:')
print('  ✓ 68-dim input features (38 original + 30 voice)')
print('  ✓ Separate voice feature processing branch')
print('  ✓ Specialized voice enhancement loss function')
print('  ✓ Optimized loss weights for voice enhancement')

# 确保使用完整的68维输入特征
main_input = Input(shape=(None, 68), name='main_input')

# 分离原始特征和人声特征
original_features = keras.layers.Lambda(lambda x: x[:, :, :38], name='original_features')(main_input)
voice_features = keras.layers.Lambda(lambda x: x[:, :, 38:], name='voice_features')(main_input)

# 基础特征处理（使用68维输入）
input_dense = Dense(32, activation='tanh', name='input_dense', kernel_constraint=constraint, bias_constraint=constraint)(main_input)

# VAD分支（基于基础特征）
vad_gru = GRU(24, activation='tanh', recurrent_activation='sigmoid', return_sequences=True, name='vad_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(input_dense)
vad_output = Dense(1, activation='sigmoid', name='vad_output', kernel_constraint=constraint, bias_constraint=constraint)(vad_gru)

# 噪声抑制分支（主要基于原始特征）
noise_input = keras.layers.concatenate([input_dense, vad_gru, original_features])
noise_gru = GRU(48, activation='relu', recurrent_activation='sigmoid', return_sequences=True, name='noise_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(noise_input)

# 主要特征融合层
denoise_input = keras.layers.concatenate([vad_gru, noise_gru, original_features])
denoise_gru = GRU(96, activation='tanh', recurrent_activation='sigmoid', return_sequences=True, name='denoise_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(denoise_input)

# 噪声抑制输出
denoise_output = Dense(18, activation='sigmoid', name='denoise_output', kernel_constraint=constraint, bias_constraint=constraint)(denoise_gru)

# 人声增强专用分支 - 重点处理人声特征
print('Building specialized voice enhancement branch...')

# 第一层：人声特征预处理
voice_preprocess = Dense(24, activation='tanh', name='voice_preprocess', kernel_constraint=constraint, bias_constraint=constraint)(voice_features)

# 第二层：融合基础特征和人声特征
voice_fusion_input = keras.layers.concatenate([denoise_gru, voice_preprocess, voice_features])
voice_fusion = Dense(64, activation='tanh', name='voice_fusion', kernel_constraint=constraint, bias_constraint=constraint)(voice_fusion_input)

# 第三层：人声专用GRU处理
voice_gru = GRU(48, activation='tanh', recurrent_activation='sigmoid', return_sequences=True, name='voice_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(voice_fusion)

# 第四层：人声增强输出
voice_output = Dense(18, activation='sigmoid', name='voice_output', kernel_constraint=constraint, bias_constraint=constraint)(voice_gru)

model = Model(inputs=main_input, outputs=[denoise_output, voice_output, vad_output])

#model.compile(loss=[mycost, my_crossentropy],
#              metrics=[msse],
#              optimizer='adam', loss_weights=[10, 0.5])


# 编译增强模型 - 使用专门的人声增强损失函数
model.compile(
    loss=[mycost, human_voice_enhancement_loss, my_crossentropy],
    loss_weights=[6, 20, 0.5],  # 大幅提高人声增强权重到20，降低噪声抑制权重避免冲突
    metrics=[msse, msse, my_accuracy],
    optimizer='adam'
)

print('Model compiled with specialized voice enhancement loss function')
print('Loss configuration:')
print('  - Noise Suppression: mycost (weight=6)')
print('  - Voice Enhancement: human_voice_enhancement_loss (weight=20) - SPECIALIZED FOR VOICE')
print('  - VAD Detection: my_crossentropy (weight=0.5)')
print('  - Total focus on voice enhancement: 20/(6+20+0.5) = 75.5%')

batch_size = 16  # 进一步减小批次大小以提高稳定性

print('Loading enhanced training data...')
with h5py.File('training_data_enhanced.h5', 'r') as hf:
    all_data = hf['data'][:]
print('done.')

window_size = 2000

# 数据格式: 68维输入 + 18维噪声目标 + 18维人声目标 + 1维VAD = 105维
nb_sequences = len(all_data)//window_size
print(f'{nb_sequences} sequences, data shape: {all_data.shape}')

# 修复：使用完整的68维输入特征（38维原始 + 30维人声增强特征）
x_train = all_data[:nb_sequences*window_size, :68]
x_train = np.reshape(x_train, (nb_sequences, window_size, 68))

# 噪声抑制目标 (18维)
noise_suppression_train = np.copy(all_data[:nb_sequences*window_size, 68:86])
noise_suppression_train = np.reshape(noise_suppression_train, (nb_sequences, window_size, 18))

# 人声增强目标 (18维)
voice_enhancement_train = np.copy(all_data[:nb_sequences*window_size, 86:104])
voice_enhancement_train = np.reshape(voice_enhancement_train, (nb_sequences, window_size, 18))

# VAD目标 (1维)
vad_train = np.copy(all_data[:nb_sequences*window_size, 104:105])
vad_train = np.reshape(vad_train, (nb_sequences, window_size, 1))

# 数据清理 - 处理NaN和无穷大值
print('Cleaning data...')
x_train = np.nan_to_num(x_train, nan=0.0, posinf=1.0, neginf=-1.0)
noise_suppression_train = np.nan_to_num(noise_suppression_train, nan=0.0, posinf=1.0, neginf=0.0)
voice_enhancement_train = np.nan_to_num(voice_enhancement_train, nan=0.0, posinf=1.0, neginf=0.0)
vad_train = np.nan_to_num(vad_train, nan=0.0, posinf=1.0, neginf=0.0)

# 归一化输入数据到合理范围
x_train = np.clip(x_train, -10.0, 10.0)  # 限制输入范围

# 确保目标值在[0,1]范围内
noise_suppression_train = np.clip(noise_suppression_train, 0.0, 1.0)
voice_enhancement_train = np.clip(voice_enhancement_train, 0.0, 1.0)
vad_train = np.clip(vad_train, 0.0, 1.0)

all_data = 0;  # 释放内存

print('Enhanced Training Data Configuration:')
print(f'  Input: {x_train.shape} (68-dim: 38 original + 30 voice features)')
print(f'  Noise suppression targets: {noise_suppression_train.shape}')
print(f'  Voice enhancement targets: {voice_enhancement_train.shape}')
print(f'  VAD targets: {vad_train.shape}')

print('Data ranges after cleaning:')
print(f'  Input: [{np.min(x_train):.3f}, {np.max(x_train):.3f}]')
print(f'  Original features (0-37): [{np.min(x_train[:,:,:38]):.3f}, {np.max(x_train[:,:,:38]):.3f}]')
print(f'  Voice features (38-67): [{np.min(x_train[:,:,38:]):.3f}, {np.max(x_train[:,:,38:]):.3f}]')
print(f'  Noise targets: [{np.min(noise_suppression_train):.3f}, {np.max(noise_suppression_train):.3f}]')
print(f'  Voice targets: [{np.min(voice_enhancement_train):.3f}, {np.max(voice_enhancement_train):.3f}]')
print(f'  VAD targets: [{np.min(vad_train):.3f}, {np.max(vad_train):.3f}]')

# 设置回调 - 保存到models_enhance目录
import os
os.makedirs("models_voice_enhanced/checkpoints", exist_ok=True)
os.makedirs("models_voice_enhanced/best_models", exist_ok=True)
os.makedirs("models_voice_enhanced/final_weights", exist_ok=True)
os.makedirs("models_voice_enhanced/training_logs", exist_ok=True)

# 每轮都保存检查点
checkpoint_path = "models_voice_enhanced/checkpoints/voice-enhanced-{epoch:02d}-{loss:.5f}.keras"
checkpoint_all = ModelCheckpoint(checkpoint_path, monitor='loss', verbose=1, save_best_only=False, mode='min')

# 保存最佳模型
best_model_path = "models_voice_enhanced/best_models/best_voice_enhanced_model.keras"
checkpoint_best = ModelCheckpoint(best_model_path, monitor='val_loss', verbose=1, save_best_only=True, mode='min')

callbacks_list = [checkpoint_all, checkpoint_best]

print('Starting SPECIALIZED Voice Enhancement Training...')
print('=' * 70)
print('🎯 VOICE ENHANCEMENT FOCUSED CONFIGURATION:')
print('  ✓ 68-dim input: 38 original + 30 VOICE-SPECIFIC features')
print('  ✓ SPECIALIZED voice enhancement loss: human_voice_enhancement_loss')
print('  ✓ Frequency-weighted for 300-3400Hz human voice range')
print('  ✓ Dedicated voice processing branch with separate GRU')
print('  ✓ Voice-focused loss weights: 75.5% on voice enhancement')
print('  ✓ Separate processing paths for noise vs voice tasks')
print('=' * 70)
model.fit(
    x_train,
    [noise_suppression_train, voice_enhancement_train, vad_train],
    batch_size=batch_size,
    epochs=120,
    validation_split=0.1,
    callbacks=callbacks_list
)

# 保存最终模型到models_voice_enhanced目录
final_model_path = "models_voice_enhanced/final_weights/voice_enhanced_rnnoise_final.keras"
model.save(final_model_path)
print(f"Voice Enhanced RNNoise training completed! Final model saved to: {final_model_path}")

# 保存训练日志
import datetime
log_path = "models_voice_enhanced/training_logs/voice_enhanced_training_log.txt"
with open(log_path, "w") as f:
    f.write(f"Voice Enhanced RNNoise Training Completed\n")
    f.write(f"Date: {datetime.datetime.now()}\n")
    f.write(f"=== SPECIALIZED VOICE ENHANCEMENT CONFIGURATION ===\n")
    f.write(f"Input dimensions: 68 (38 original + 30 voice enhancement features)\n")
    f.write(f"Output dimensions: 18 noise suppression + 18 voice enhancement + 1 VAD\n")
    f.write(f"Loss functions: mycost + human_voice_enhancement_loss + my_crossentropy\n")
    f.write(f"Loss weights: [6, 20, 0.5] (Noise:Voice:VAD) - 75.5% focus on voice\n")
    f.write(f"Architecture: Specialized voice processing branch with dedicated GRU\n")
    f.write(f"Voice features: Separate processing of 30-dim voice features\n")
    f.write(f"Voice loss: Frequency-weighted for 300-3400Hz human voice range\n")
    f.write(f"Training sequences: {nb_sequences}\n")
    f.write(f"Batch size: {batch_size}\n")
    f.write(f"Epochs: 120\n")
    f.write(f"Final model: {final_model_path}\n")
    f.write(f"Best model: {best_model_path}\n")

print(f"Training log saved to: {log_path}")
