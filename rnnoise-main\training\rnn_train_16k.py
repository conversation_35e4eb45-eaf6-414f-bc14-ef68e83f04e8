#!/usr/bin/python

from __future__ import print_function
import tensorflow as tf
import keras
from keras.models import Sequential
from keras.models import Model
from keras.layers import Input
from keras.callbacks import ModelCheckpoint
from keras.layers import Dense
from keras.layers import LSTM
from keras.layers import GRU
from keras.layers import SimpleRNN
from keras.layers import Dropout
from keras.layers import concatenate
from keras import losses
from keras import regularizers
from keras.constraints import min_max_norm
import h5py
from keras.constraints import Constraint
import tensorflow as tf
from keras import backend as K
import numpy as np

# 设置CPU运行
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
#import tensorflow as tf
#from keras.backend.tensorflow_backend import set_session
#config = tf.ConfigProto()
#config.gpu_options.per_process_gpu_memory_fraction = 0.42
#set_session(tf.Session(config=config))


def my_crossentropy(y_true, y_pred):
    """适配VAD输出的交叉熵损失函数"""
    return tf.keras.losses.binary_crossentropy(y_true, y_pred)

def human_voice_loss(y_true, y_pred):
    """专门的人声增强损失函数 - 基于人声频谱特征优化"""
    mask = tf.minimum(y_true + 1., 1.)

    # 人声频段权重 (基于人声频谱特征: 300-3400Hz为主频段)
    voice_weights = tf.constant([
        1.2, 1.8, 2.0, 2.2, 2.0, 1.8, 1.5, 1.2,  # 300-2000Hz 人声主频段
        1.0, 0.8, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1,  # 高频衰减
        0.1, 0.1  # 超高频
    ], dtype=tf.float32)

    # 基础损失：平方根误差（更适合人声）
    base_loss = tf.square(tf.sqrt(y_pred) - tf.sqrt(y_true))

    # 人声特征加权损失
    voice_weighted_loss = voice_weights * base_loss

    # 清晰度损失：保持人声的动态特征
    clarity_loss = tf.square(y_pred - y_true) * 0.1

    # 总损失
    total_loss = voice_weighted_loss + clarity_loss
    return tf.reduce_mean(mask * total_loss, axis=-1)

def mymask(y_true):
    return tf.minimum(y_true+1., 1.)

def msse(y_true, y_pred):
    return tf.reduce_mean(mymask(y_true) * tf.square(tf.sqrt(y_pred) - tf.sqrt(y_true)), axis=-1)

def mycost(y_true, y_pred):
    # 确保输入维度正确
    mask = mymask(y_true)
    sqrt_diff = tf.sqrt(tf.maximum(y_pred, 1e-8)) - tf.sqrt(tf.maximum(y_true, 1e-8))
    mse_loss = tf.square(sqrt_diff)
    quartic_loss = tf.square(mse_loss)

    # 简化损失函数，避免维度问题
    total_loss = mask * (10 * quartic_loss + mse_loss)
    return tf.reduce_mean(total_loss, axis=-1)

def my_accuracy(y_true, y_pred):
    return tf.reduce_mean(2*tf.abs(y_true-0.5) * tf.cast(tf.equal(y_true, tf.round(y_pred)), tf.float32), axis=-1)

# 人声增强损失函数
def voice_enhancement_loss(y_true, y_pred):
    """人声增强损失函数"""
    mask = tf.minimum(y_true + 1., 1.)

    # 基础MSE损失，确保数值稳定性
    sqrt_pred = tf.sqrt(tf.maximum(y_pred, 1e-8))
    sqrt_true = tf.sqrt(tf.maximum(y_true, 1e-8))
    mse = tf.square(sqrt_pred - sqrt_true)

    # 高频增强权重 (对高频成分给予更多关注)
    freq_weights = tf.constant([1.0, 1.0, 1.1, 1.1, 1.2, 1.2, 1.3, 1.3, 1.4,
                               1.4, 1.5, 1.5, 1.6, 1.6, 1.7, 1.7, 1.8, 1.8])
    freq_weights = tf.reshape(freq_weights, [1, 1, 18])  # 确保维度匹配
    weighted_mse = mse * freq_weights

    # 谐波保持损失 (确保自然度)
    harmonic_loss = tf.square(y_pred - y_true) * 0.1

    total_loss = weighted_mse + harmonic_loss
    return tf.reduce_mean(mask * total_loss, axis=-1)

# 组合损失函数 (用于单输出模型的兼容性)
def combined_enhancement_loss(y_true, y_pred):
    """组合的噪声抑制和人声增强损失"""
    # y_true和y_pred都是36维 (18维噪声抑制 + 18维人声增强)
    noise_true = y_true[:, :, :18]
    noise_pred = y_pred[:, :, :18]
    voice_true = y_true[:, :, 18:]
    voice_pred = y_pred[:, :, 18:]

    noise_loss = mycost(noise_true, noise_pred)
    voice_loss = voice_enhancement_loss(voice_true, voice_pred)

    # 加权组合
    return 10.0 * noise_loss + 8.0 * voice_loss

class WeightClip(Constraint):
    '''Clips the weights incident to each hidden unit to be inside a range
    '''
    def __init__(self, c=2):
        self.c = c

    def __call__(self, p):
        return tf.clip_by_value(p, -self.c, self.c)

    def get_config(self):
        return {'name': self.__class__.__name__,
            'c': self.c}


reg = 0.000001
constraint = WeightClip(0.499)

print('Build enhanced model...')
# 修复：使用完整的68维输入特征（38维原始 + 30维人声增强特征）
main_input = Input(shape=(None, 68), name='main_input')

# 基础特征处理（适配68维输入）
tmp = Dense(32, activation='tanh', name='input_dense', kernel_constraint=constraint, bias_constraint=constraint)(main_input)

# VAD分支
vad_gru = GRU(24, activation='tanh', recurrent_activation='sigmoid', return_sequences=True, name='vad_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(tmp)
vad_output = Dense(1, activation='sigmoid', name='vad_output', kernel_constraint=constraint, bias_constraint=constraint)(vad_gru)

# 噪声抑制分支
noise_input = keras.layers.concatenate([tmp, vad_gru, main_input])
noise_gru = GRU(48, activation='relu', recurrent_activation='sigmoid', return_sequences=True, name='noise_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(noise_input)

# 主要特征融合层
denoise_input = keras.layers.concatenate([vad_gru, noise_gru, main_input])
denoise_gru = GRU(96, activation='tanh', recurrent_activation='sigmoid', return_sequences=True, name='denoise_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(denoise_input)

# 噪声抑制输出
denoise_output = Dense(18, activation='sigmoid', name='denoise_output', kernel_constraint=constraint, bias_constraint=constraint)(denoise_gru)

# 人声增强专用分支
voice_specific_input = keras.layers.concatenate([denoise_gru, main_input[:, :, 38:]])  # 使用人声增强特征
voice_specific = Dense(48, activation='tanh', name='voice_specific', kernel_constraint=constraint, bias_constraint=constraint)(voice_specific_input)
voice_output = Dense(18, activation='sigmoid', name='voice_output', kernel_constraint=constraint, bias_constraint=constraint)(voice_specific)

model = Model(inputs=main_input, outputs=[denoise_output, voice_output, vad_output])

#model.compile(loss=[mycost, my_crossentropy],
#              metrics=[msse],
#              optimizer='adam', loss_weights=[10, 0.5])


# 编译增强模型 - 使用专门的人声增强损失函数
model.compile(
    loss=[mycost, human_voice_loss, my_crossentropy],
    loss_weights=[8, 15, 0.5],  # 提高人声增强权重到15
    metrics=[msse, msse, my_accuracy],
    optimizer='adam'
)

print('Model compiled with enhanced voice loss function')
print('Loss weights: Noise Suppression=8, Voice Enhancement=15, VAD=0.5')

batch_size = 16  # 进一步减小批次大小以提高稳定性

print('Loading enhanced training data...')
with h5py.File('training_data_enhanced.h5', 'r') as hf:
    all_data = hf['data'][:]
print('done.')

window_size = 2000

# 数据格式: 68维输入 + 18维噪声目标 + 18维人声目标 + 1维VAD = 105维
nb_sequences = len(all_data)//window_size
print(f'{nb_sequences} sequences, data shape: {all_data.shape}')

# 修复：使用完整的68维输入特征（38维原始 + 30维人声增强特征）
x_train = all_data[:nb_sequences*window_size, :68]
x_train = np.reshape(x_train, (nb_sequences, window_size, 68))

# 噪声抑制目标 (18维)
noise_suppression_train = np.copy(all_data[:nb_sequences*window_size, 68:86])
noise_suppression_train = np.reshape(noise_suppression_train, (nb_sequences, window_size, 18))

# 人声增强目标 (18维)
voice_enhancement_train = np.copy(all_data[:nb_sequences*window_size, 86:104])
voice_enhancement_train = np.reshape(voice_enhancement_train, (nb_sequences, window_size, 18))

# VAD目标 (1维)
vad_train = np.copy(all_data[:nb_sequences*window_size, 104:105])
vad_train = np.reshape(vad_train, (nb_sequences, window_size, 1))

# 数据清理 - 处理NaN和无穷大值
print('Cleaning data...')
x_train = np.nan_to_num(x_train, nan=0.0, posinf=1.0, neginf=-1.0)
noise_suppression_train = np.nan_to_num(noise_suppression_train, nan=0.0, posinf=1.0, neginf=0.0)
voice_enhancement_train = np.nan_to_num(voice_enhancement_train, nan=0.0, posinf=1.0, neginf=0.0)
vad_train = np.nan_to_num(vad_train, nan=0.0, posinf=1.0, neginf=0.0)

# 归一化输入数据到合理范围
x_train = np.clip(x_train, -10.0, 10.0)  # 限制输入范围

# 确保目标值在[0,1]范围内
noise_suppression_train = np.clip(noise_suppression_train, 0.0, 1.0)
voice_enhancement_train = np.clip(voice_enhancement_train, 0.0, 1.0)
vad_train = np.clip(vad_train, 0.0, 1.0)

all_data = 0;  # 释放内存

print('Enhanced Training Data Configuration:')
print(f'  Input: {x_train.shape} (68-dim: 38 original + 30 voice features)')
print(f'  Noise suppression targets: {noise_suppression_train.shape}')
print(f'  Voice enhancement targets: {voice_enhancement_train.shape}')
print(f'  VAD targets: {vad_train.shape}')

print('Data ranges after cleaning:')
print(f'  Input: [{np.min(x_train):.3f}, {np.max(x_train):.3f}]')
print(f'  Original features (0-37): [{np.min(x_train[:,:,:38]):.3f}, {np.max(x_train[:,:,:38]):.3f}]')
print(f'  Voice features (38-67): [{np.min(x_train[:,:,38:]):.3f}, {np.max(x_train[:,:,38:]):.3f}]')
print(f'  Noise targets: [{np.min(noise_suppression_train):.3f}, {np.max(noise_suppression_train):.3f}]')
print(f'  Voice targets: [{np.min(voice_enhancement_train):.3f}, {np.max(voice_enhancement_train):.3f}]')
print(f'  VAD targets: [{np.min(vad_train):.3f}, {np.max(vad_train):.3f}]')

# 设置回调 - 保存到models_enhance目录
import os
os.makedirs("models_enhance_v2/checkpoints", exist_ok=True)
os.makedirs("models_enhance_v2/best_models", exist_ok=True)
os.makedirs("models_enhance_v2/final_weights", exist_ok=True)
os.makedirs("models_enhance_v2/training_logs", exist_ok=True)

# 每轮都保存检查点
checkpoint_path = "models_enhance_v2/checkpoints/enhanced-v2-weights-{epoch:02d}-{loss:.5f}.keras"
checkpoint_all = ModelCheckpoint(checkpoint_path, monitor='loss', verbose=1, save_best_only=False, mode='min')

# 保存最佳模型
best_model_path = "models_enhance_v2/best_models/best_enhanced_v2_model.keras"
checkpoint_best = ModelCheckpoint(best_model_path, monitor='val_loss', verbose=1, save_best_only=True, mode='min')

callbacks_list = [checkpoint_all, checkpoint_best]

print('Starting Enhanced Voice Training with Fixed Configuration...')
print('Key Improvements:')
print('  ✓ Using full 68-dim input features (38 original + 30 voice)')
print('  ✓ Specialized human_voice_loss function for voice enhancement')
print('  ✓ Dedicated voice processing branch')
print('  ✓ Optimized loss weights (Noise:8, Voice:15, VAD:0.5)')
print('=' * 60)
model.fit(
    x_train,
    [noise_suppression_train, voice_enhancement_train, vad_train],
    batch_size=batch_size,
    epochs=120,
    validation_split=0.1,
    callbacks=callbacks_list
)

# 保存最终模型到models_enhance_v2目录
final_model_path = "models_enhance_v2/final_weights/enhanced_v2_rnnoise_final.keras"
model.save(final_model_path)
print(f"Enhanced V2 model training completed! Final model saved to: {final_model_path}")

# 保存训练日志
import datetime
log_path = "models_enhance_v2/training_logs/training_log_v2.txt"
with open(log_path, "w") as f:
    f.write(f"Enhanced RNNoise V2 Training Completed\n")
    f.write(f"Date: {datetime.datetime.now()}\n")
    f.write(f"=== FIXED CONFIGURATION ===\n")
    f.write(f"Input dimensions: 68 (38 original + 30 voice enhancement features)\n")
    f.write(f"Output dimensions: 18 noise suppression + 18 voice enhancement + 1 VAD\n")
    f.write(f"Loss functions: mycost + human_voice_loss + my_crossentropy\n")
    f.write(f"Loss weights: [8, 15, 0.5] (Noise:Voice:VAD)\n")
    f.write(f"Architecture: Dedicated voice processing branch\n")
    f.write(f"Training sequences: {nb_sequences}\n")
    f.write(f"Batch size: {batch_size}\n")
    f.write(f"Epochs: 120\n")
    f.write(f"Final model: {final_model_path}\n")
    f.write(f"Best model: {best_model_path}\n")

print(f"Training log saved to: {log_path}")
