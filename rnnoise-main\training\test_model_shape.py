#!/usr/bin/env python3
"""
测试模型输出形状
"""

import tensorflow as tf
import keras
from keras.models import Sequential
from keras.models import Model
from keras.layers import Input
from keras.layers import Dense
from keras.layers import LSTM
from keras.layers import GRU
from keras.layers import SimpleRNN
from keras.layers import Dropout
from keras.layers import concatenate
from keras import losses
from keras import regularizers
from keras.constraints import min_max_norm
import h5py
from keras.constraints import Constraint
import tensorflow as tf
from keras import backend as K
import numpy as np

# 设置CPU运行
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

class WeightClip(Constraint):
    def __init__(self, c=2):
        self.c = c

    def __call__(self, p):
        return tf.clip_by_value(p, -self.c, self.c)

    def get_config(self):
        return {'name': self.__class__.__name__,
            'c': self.c}

reg = 0.000001
constraint = WeightClip(0.499)

print('Build test model...')
main_input = Input(shape=(None, 68), name='main_input')

# 分离输入特征
noise_features = main_input[:, :, :38]  # 前38维用于噪声抑制
voice_features = main_input[:, :, 38:]  # 后30维用于人声增强

# 共享特征处理层
shared_dense = Dense(32, activation='tanh', name='shared_dense', kernel_constraint=constraint, bias_constraint=constraint)(main_input)

# VAD分支 (基于噪声抑制特征)
vad_input = keras.layers.concatenate([shared_dense, noise_features])
vad_gru = GRU(24, activation='tanh', recurrent_activation='sigmoid', return_sequences=True, name='vad_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(vad_input)
vad_output = Dense(1, activation='sigmoid', name='vad_output', kernel_constraint=constraint, bias_constraint=constraint)(vad_gru)

# 噪声抑制分支
noise_input = keras.layers.concatenate([shared_dense, vad_gru, noise_features])
noise_gru = GRU(48, activation='relu', recurrent_activation='sigmoid', return_sequences=True, name='noise_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(noise_input)

# 人声增强分支
voice_input = keras.layers.concatenate([shared_dense, vad_gru, voice_features])
voice_gru = GRU(48, activation='tanh', recurrent_activation='sigmoid', return_sequences=True, name='voice_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(voice_input)

# 特征融合层
fused_input = keras.layers.concatenate([vad_gru, noise_gru, voice_gru, main_input])
final_gru = GRU(96, activation='tanh', recurrent_activation='sigmoid', return_sequences=True, name='final_gru', kernel_regularizer=regularizers.l2(reg), recurrent_regularizer=regularizers.l2(reg), kernel_constraint=constraint, recurrent_constraint=constraint, bias_constraint=constraint)(fused_input)

# 双重输出
noise_suppression_output = Dense(18, activation='sigmoid', name='noise_suppression_output', kernel_constraint=constraint, bias_constraint=constraint)(final_gru)
voice_enhancement_output = Dense(18, activation='sigmoid', name='voice_enhancement_output', kernel_constraint=constraint, bias_constraint=constraint)(final_gru)

# 构建增强模型
model = Model(inputs=main_input, outputs=[noise_suppression_output, voice_enhancement_output, vad_output])

print('Model summary:')
model.summary()

# 测试输出形状
test_input = np.random.random((1, 100, 68))
outputs = model.predict(test_input)

print('\nOutput shapes:')
for i, output in enumerate(outputs):
    print(f'Output {i}: {output.shape}')

print('\nOutput names:')
for i, output_name in enumerate(model.output_names):
    print(f'Output {i}: {output_name}')
