# 人声增强功能问题修复指南

## 问题描述
RNNoise增强版的人声增强功能训练后效果不明显，需要系统性排查和修复。

## 问题清单

### 1. 输入特征维度不匹配
**文件**: `training/rnn_train_16k.py`
**问题**: 
```python
# 当前代码 - 错误
main_input = Input(shape=(None, 38), name='main_input')  # 应该是68维
x_train = all_data[:nb_sequences*window_size, :38]       # 只使用了38维特征
```
**修复要求**:
- 将输入维度从38改为68
- 确保使用完整的68维特征进行训练
- 验证训练数据确实包含68维特征

### 2. 损失函数权重配置不当
**文件**: `training/rnn_train_16k.py`
**问题**:
```python
# 当前权重配置
model.compile(
    loss=[mycost, mycost, my_crossentropy],  # 人声增强使用了错误的损失函数
    loss_weights=[10, 8, 0.5],              # 人声增强权重偏低
)
```
**修复要求**:
- 为人声增强创建专门的损失函数
- 调整权重比例，提高人声增强的重要性
- 损失函数应针对人声频段特征优化

### 3. 人声增强损失函数设计问题
**文件**: `training/rnn_train_16k.py`
**当前问题**:
```python
def voice_enhancement_loss(y_true, y_pred):
    # 权重设计不符合人声特征
    freq_weights = [1.0, 1.0, 1.1, 1.1, ..., 1.8, 1.8]  # 线性递增不合理
```
**修复要求**:
- 重新设计频率权重，突出人声主频段(300-3400Hz)
- 添加人声清晰度损失项
- 考虑谐波结构保持

### 4. 编译配置问题
**文件**: `src/denoise.c`, `Makefile`
**问题**:
- ENHANCED宏可能未正确定义
- 编译时可能使用了旧的38维配置
- 权重文件可能未更新

**修复要求**:
- 确保编译时定义ENHANCED宏
- 验证TOTAL_INPUT_FEATURES=68
- 更新权重文件到最新训练结果

### 5. 推理阶段特征提取问题
**文件**: `src/denoise.c`
**问题**:
```c
// 可能的问题
float features[NB_FEATURES];  // 可能仍是38维
// 应该是
float features[TOTAL_INPUT_FEATURES];  // 68维
```
**修复要求**:
- 确保推理时使用68维特征
- 验证人声增强特征正确提取
- 添加调试输出验证特征维度

### 6. 网络架构优化
**文件**: `training/rnn_train_16k.py`
**问题**:
- 人声增强和噪声抑制共享相同的特征提取层
- 缺乏专门的人声处理分支

**修复要求**:
- 为人声增强添加专门的处理分支
- 考虑使用注意力机制突出人声特征
- 优化网络结构以更好地分离人声和噪声

## 具体修复代码示例

### 修复1: 输入维度
```python
# 在 rnn_train_16k.py 中修改
main_input = Input(shape=(None, 68), name='main_input')  # 改为68维
x_train = all_data[:nb_sequences*window_size, :68]       # 使用全部特征
```

### 修复2: 人声增强损失函数
```python
def human_voice_enhancement_loss(y_true, y_pred):
    """专门的人声增强损失函数"""
    mask = tf.minimum(y_true + 1., 1.)
    
    # 人声频段权重 (基于人声频谱分布)
    voice_freq_weights = tf.constant([
        1.2, 1.8, 2.2, 2.5, 2.2, 1.8, 1.5, 1.2,  # 300-2000Hz 主频段
        1.0, 0.8, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1,  # 2000Hz以上衰减
        0.1, 0.1  # 超高频
    ], dtype=tf.float32)
    voice_freq_weights = tf.reshape(voice_freq_weights, [1, 1, 18])
    
    # 基础MSE损失
    mse_loss = tf.square(y_pred - y_true)
    
    # 加权损失 (突出人声频段)
    weighted_loss = voice_freq_weights * mse_loss
    
    # 清晰度损失 (保持人声细节)
    clarity_loss = tf.square(
        tf.nn.conv1d(y_pred, tf.constant([[[1., -1.]]]), 1, 'SAME') - 
        tf.nn.conv1d(y_true, tf.constant([[[1., -1.]]]), 1, 'SAME')
    ) * 0.1
    
    total_loss = weighted_loss + clarity_loss
    return tf.reduce_mean(mask * total_loss)
```

### 修复3: 训练配置
```python
# 更新模型编译配置
model.compile(
    loss=[mycost, human_voice_enhancement_loss, my_crossentropy],
    loss_weights=[8, 15, 0.5],  # 提高人声增强权重
    metrics=[msse, msse, my_accuracy],
    optimizer=Adam(learning_rate=0.001)
)
```

### 修复4: 编译配置
```c
// 在 denoise.c 开头确保定义
#define ENHANCED 1
#define TOTAL_INPUT_FEATURES 68
#define VOICE_ENHANCEMENT_FEATURES 30

// 在 rnnoise_process_frame 中添加调试
#ifdef DEBUG
printf("Using %d input features\n", TOTAL_INPUT_FEATURES);
#endif
```

## 验证步骤

### 1. 训练验证
```bash
# 检查训练数据维度
python -c "import h5py; f=h5py.File('training_data_enhanced.h5','r'); print('Input shape:', f['input_data'].shape)"

# 训练时监控损失
python rnn_train_16k.py --verbose=1 --monitor-losses
```

### 2. 编译验证
```bash
# 重新编译确保使用新配置
make clean
make CFLAGS="-DENHANCED=1 -DDEBUG=1"

# 验证可执行文件
./rnnoise_enhanced --version  # 应显示enhanced版本信息
```

### 3. 效果验证
```bash
# 测试人声增强效果
./rnnoise_enhanced input.wav output.wav
# 使用PESQ/STOI工具评估人声质量改善
```

## 预期结果

修复后应该看到：
1. 训练时人声增强损失正常下降
2. 推理时使用68维特征
3. 人声清晰度和自然度明显改善
4. PESQ/STOI指标提升15-25%

## 优先级

1. **高优先级**: 修复输入维度问题 (问题1)
2. **高优先级**: 修复损失函数 (问题2,3)
3. **中优先级**: 验证编译配置 (问题4,5)
4. **低优先级**: 网络架构优化 (问题6)

请按照优先级顺序逐一修复，每修复一个问题后重新训练和测试效果。